using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Amazon.S3;
using Amazon.S3.Model;
using Amazon.S3.Transfer;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;

namespace YendorCats.API.Services
{
    public class S3StorageService : IS3StorageService
    {
        private readonly IAmazonS3 _s3Client;
        private readonly ILogger<S3StorageService> _logger;
        private readonly IConfiguration _configuration;
        private readonly IMemoryCache _memoryCache;
        private readonly string _bucketName;
        private readonly string _cdnDomain;
        private readonly bool _useCdn;
        private readonly bool _useDirectS3Urls;
        private readonly string _serviceUrl;
        private readonly string _publicUrlTemplate;
        private readonly string _keyPrefix;
        
        // Cache configuration
        private readonly TimeSpan _metadataCacheExpiry = TimeSpan.FromMinutes(15);
        private readonly TimeSpan _fileCacheExpiry = TimeSpan.FromMinutes(5);
        private const string METADATA_CACHE_PREFIX = "s3_metadata_";
        private const string FILES_CACHE_PREFIX = "s3_files_";

        public S3StorageService(
            IAmazonS3 s3Client,
            IConfiguration configuration,
            ILogger<S3StorageService> logger,
            IMemoryCache memoryCache)
        {
            _s3Client = s3Client;
            _logger = logger;
            _configuration = configuration;
            _memoryCache = memoryCache;

            _bucketName = configuration["AWS:S3:BucketName"]
                ?? throw new ArgumentNullException("AWS:S3:BucketName", "S3 bucket name is not configured");

            _useDirectS3Urls = bool.TryParse(configuration["AWS:S3:UseDirectS3Urls"], out bool useDirectS3Urls) && useDirectS3Urls;
            _serviceUrl = configuration["AWS:S3:ServiceUrl"] ?? "https://s3.amazonaws.com";
            _publicUrlTemplate = configuration["AWS:S3:PublicUrl"] ?? "https://{bucket}.s3.amazonaws.com/{key}";
            _useCdn = bool.TryParse(configuration["AWS:S3:UseCdn"], out bool useCdn) && useCdn;
            _cdnDomain = configuration["AWS:S3:CdnDomain"] ?? string.Empty;
            _keyPrefix = configuration["AWS:S3:KeyPrefix"] ?? string.Empty;

            if (!string.IsNullOrEmpty(_serviceUrl))
            {
                _logger.LogInformation("Using S3 client with endpoint: {ServiceUrl}", _serviceUrl);
            }

            _logger.LogInformation("S3StorageService initialized with bucket: {BucketName}, UseDirectS3Urls: {UseDirectS3Urls}, UseCDN: {UseCDN}, KeyPrefix: {KeyPrefix}",
                _bucketName, _useDirectS3Urls, _useCdn, _keyPrefix);
        }

        public async Task<UploadResult> UploadFileAsync(Stream fileStream, string fileName, string category)
        {
            _logger.LogInformation("Uploading file to S3: {FileName}, Category: {Category}",
                fileName, category);

            try
            {
                // Organize by category
                var key = string.IsNullOrEmpty(category) ? fileName : $"{category}/{fileName}";
                var prefixedKey = ApplyKeyPrefix(key);

                using var transferUtility = new TransferUtility(_s3Client);
                var uploadRequest = new TransferUtilityUploadRequest
                {
                    InputStream = fileStream,
                    BucketName = _bucketName,
                    Key = prefixedKey,
                    ContentType = GetContentType(fileName),
                    CannedACL = S3CannedACL.PublicRead
                };

                // Add category metadata
                uploadRequest.Metadata.Add("category", category ?? "general");

                await transferUtility.UploadAsync(uploadRequest);

                // Invalidate file list cache since a new file was uploaded
                InvalidateFileListCache();

                return new UploadResult
                {
                    Success = true,
                    Url = GetFileUrl(prefixedKey),
                    FileId = prefixedKey,
                    FileSize = fileStream.Length,
                    UploadedAt = DateTime.UtcNow
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading file to S3: {FileName}", fileName);
                return new UploadResult
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        public async Task<string> UploadFileWithMetadataAsync(Stream fileStream, string fileName, string contentType, Dictionary<string, string> metadata)
        {
            _logger.LogInformation("Uploading file to S3 with metadata: {FileName}, ContentType: {ContentType}, MetadataCount: {MetadataCount}",
                fileName, contentType, metadata?.Count ?? 0);

            try
            {
                var prefixedKey = ApplyKeyPrefix(fileName);
                var putRequest = new PutObjectRequest
                {
                    BucketName = _bucketName,
                    Key = prefixedKey,
                    InputStream = fileStream,
                    ContentType = contentType,
                    CannedACL = S3CannedACL.PublicRead
                };

                if (metadata != null && metadata.Count > 0)
                {
                    foreach (var kvp in metadata)
                    {
                        putRequest.Metadata.Add(kvp.Key, kvp.Value);
                    }
                }

                await _s3Client.PutObjectAsync(putRequest);
                
                // Invalidate file list cache since a new file was uploaded
                InvalidateFileListCache();
                
                return GetFileUrl(prefixedKey);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading file with metadata to S3: {FileName}", fileName);
                throw;
            }
        }



        public async Task<string> GetPreSignedUrlAsync(string fileName, int expiryMinutes = 60)
        {
            _logger.LogInformation("Generating pre-signed URL for file: {FileName}, ExpiryMinutes: {ExpiryMinutes}",
                fileName, expiryMinutes);

            try
            {
                var prefixedKey = ApplyKeyPrefix(fileName);
                var request = new GetPreSignedUrlRequest
                {
                    BucketName = _bucketName,
                    Key = prefixedKey,
                    Expires = DateTime.UtcNow.AddMinutes(expiryMinutes)
                };
                var url = await Task.FromResult(_s3Client.GetPreSignedURL(request));
                _logger.LogInformation("Generated pre-signed URL for file: {FileName}", fileName);
                return url;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating pre-signed URL for file: {FileName}", fileName);
                throw;
            }
        }

        public async Task<Dictionary<string, string>> GetObjectMetadataAsync(string fileName)
        {
            var cacheKey = $"{METADATA_CACHE_PREFIX}{fileName}";
            
            // Try to get from cache first
            if (_memoryCache.TryGetValue(cacheKey, out Dictionary<string, string> cachedMetadata))
            {
                _logger.LogDebug("Retrieved metadata from cache for file: {FileName}", fileName);
                return cachedMetadata;
            }

            _logger.LogInformation("Getting metadata for file: {FileName}", fileName);

            try
            {
                var prefixedKey = ApplyKeyPrefix(fileName);
                var request = new GetObjectMetadataRequest
                {
                    BucketName = _bucketName,
                    Key = prefixedKey
                };
                var response = await _s3Client.GetObjectMetadataAsync(request);
                var metadata = new Dictionary<string, string>();
                foreach (var key in response.Metadata.Keys)
                {
                    metadata[key] = response.Metadata[key];
                }
                
                // Cache the metadata
                var cacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = _metadataCacheExpiry,
                    SlidingExpiration = TimeSpan.FromMinutes(5),
                    Priority = CacheItemPriority.Normal
                };
                _memoryCache.Set(cacheKey, metadata, cacheOptions);
                
                _logger.LogInformation("Retrieved and cached metadata for file: {FileName}, MetadataCount: {MetadataCount}",
                    fileName, metadata.Count);
                return metadata;
            }
            catch (AmazonS3Exception ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                _logger.LogWarning("File not found when retrieving metadata: {FileName}", fileName);
                var emptyMetadata = new Dictionary<string, string>();
                
                // Cache empty result for a shorter time to avoid repeated lookups
                var cacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(2),
                    Priority = CacheItemPriority.Low
                };
                _memoryCache.Set(cacheKey, emptyMetadata, cacheOptions);
                
                return emptyMetadata;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving metadata for file: {FileName}", fileName);
                throw;
            }
        }

        public async Task ConfigureCorsAsync()
        {
            _logger.LogInformation("Configuring CORS for S3 bucket: {BucketName}", _bucketName);

            try
            {
                var corsConfiguration = new CORSConfiguration
                {
                    Rules = new List<CORSRule>
                    {
                        new CORSRule
                        {
                            AllowedHeaders = new List<string> { "*" },
                            AllowedMethods = new List<string> { "GET", "PUT", "POST", "DELETE", "HEAD" },
                            AllowedOrigins = new List<string>
                            {
                                // Local development
                                "http://localhost:5000",
                                "https://localhost:5001",
                                "http://localhost:80",
                                "http://localhost:3000",

                                // Docker/Container environments
                                "http://frontend",
                                "http://frontend:80",
                                "http://uploader",
                                "http://uploader:80",

                                // Production domains
                                "https://yendorcats.com",
                                "https://www.yendorcats.com"
                            },
                            ExposeHeaders = new List<string>
                            {
                                "ETag",
                                "x-amz-server-side-encryption",
                                "x-amz-request-id",
                                "x-amz-id-2"
                            },
                            MaxAgeSeconds = 3600
                        }
                    }
                };
                var putCorsRequest = new PutCORSConfigurationRequest
                {
                    BucketName = _bucketName,
                    Configuration = corsConfiguration
                };
                await _s3Client.PutCORSConfigurationAsync(putCorsRequest);
                _logger.LogInformation("CORS configuration set for S3 bucket: {BucketName}", _bucketName);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error configuring CORS for S3 bucket: {BucketName} - This may be due to insufficient permissions. Continuing without CORS.", _bucketName);
                // Don't throw the exception, just log it as a warning since CORS configuration is not critical for basic functionality
            }
        }

        public async Task<List<S3Object>> ListFilesAsync(string prefix)
        {
            var cacheKey = $"{FILES_CACHE_PREFIX}{prefix ?? ""}";
            
            // Try to get from cache first
            if (_memoryCache.TryGetValue(cacheKey, out List<S3Object> cachedFiles))
            {
                _logger.LogDebug("Retrieved file list from cache for prefix: {Prefix}", prefix);
                return cachedFiles;
            }

            _logger.LogInformation("Listing files in S3 with prefix: {Prefix}", prefix);
            
            try
            {
                // Apply key prefix to the search prefix
                var prefixedSearch = ApplyKeyPrefix(prefix ?? "");
                var request = new ListObjectsV2Request
                {
                    BucketName = _bucketName,
                    Prefix = prefixedSearch
                };
                var response = await _s3Client.ListObjectsV2Async(request);
                
                // Cache the file list
                var cacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = _fileCacheExpiry,
                    Priority = CacheItemPriority.Normal
                };
                _memoryCache.Set(cacheKey, response.S3Objects, cacheOptions);
                
                _logger.LogInformation("Retrieved and cached {FileCount} files for prefix: {Prefix}",
                    response.S3Objects.Count, prefix);
                return response.S3Objects;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error listing files with prefix: {Prefix}", prefix);
                throw;
            }
        }

        public async Task UpdateObjectMetadataAsync(string fileName, Dictionary<string, string> metadata)
        {
            _logger.LogInformation("Updating metadata for S3 object: {FileName}, MetadataCount: {MetadataCount}",
                fileName, metadata?.Count ?? 0);

            try
            {
                var prefixedKey = ApplyKeyPrefix(fileName);
                
                // Get current object to preserve existing content
                var getRequest = new GetObjectRequest
                {
                    BucketName = _bucketName,
                    Key = prefixedKey
                };

                var getResponse = await _s3Client.GetObjectAsync(getRequest);

                // Create copy request with new metadata
                var copyRequest = new CopyObjectRequest
                {
                    SourceBucket = _bucketName,
                    SourceKey = prefixedKey,
                    DestinationBucket = _bucketName,
                    DestinationKey = prefixedKey,
                    ContentType = getResponse.Headers.ContentType,
                    CannedACL = S3CannedACL.PublicRead,
                    MetadataDirective = S3MetadataDirective.REPLACE
                };

                // Add new metadata
                if (metadata != null && metadata.Count > 0)
                {
                    foreach (var kvp in metadata)
                    {
                        copyRequest.Metadata.Add(kvp.Key, kvp.Value);
                    }
                }

                await _s3Client.CopyObjectAsync(copyRequest);
                
                // Invalidate cache for this object
                var cacheKey = $"{METADATA_CACHE_PREFIX}{fileName}";
                _memoryCache.Remove(cacheKey);
                
                // Also invalidate file list cache that might contain this file
                InvalidateFileListCache();
                
                _logger.LogInformation("Successfully updated metadata for S3 object: {FileName}", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating metadata for S3 object: {FileName}", fileName);
                throw;
            }
        }

        public async Task<Dictionary<string, object>> GetS3ConfigurationAsync()
        {
            _logger.LogInformation("Retrieving S3 configuration information");

            try
            {
                var config = new Dictionary<string, object>
                {
                    ["bucketName"] = _bucketName,
                    ["useCdn"] = _useCdn,
                    ["cdnDomain"] = _cdnDomain ?? string.Empty,
                    ["useDirectS3Urls"] = _useDirectS3Urls,
                    ["publicUrlTemplate"] = _publicUrlTemplate,
                    ["serviceUrl"] = _serviceUrl
                };

                // Get bucket location
                try
                {
                    var locationRequest = new GetBucketLocationRequest
                    {
                        BucketName = _bucketName
                    };
                    var locationResponse = await _s3Client.GetBucketLocationAsync(locationRequest);
                    config["region"] = locationResponse.Location.Value;
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Could not retrieve bucket location");
                    config["region"] = "unknown";
                }

                return config;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving S3 configuration");
                throw;
            }
        }

        public async Task<List<S3ObjectWithMetadata>> SearchByMetadataAsync(Dictionary<string, string> metadataFilters, string prefix = "")
        {
            _logger.LogInformation("Searching S3 objects by metadata with {FilterCount} filters and prefix: {Prefix}",
                metadataFilters?.Count ?? 0, prefix);

            try
            {
                var results = new List<S3ObjectWithMetadata>();
                var allObjects = await ListFilesAsync(prefix);

                foreach (var s3Object in allObjects)
                {
                    try
                    {
                        var metadata = await GetObjectMetadataAsync(s3Object.Key);
                        var matches = true;

                        // Check if all metadata filters match
                        if (metadataFilters != null && metadataFilters.Count > 0)
                        {
                            foreach (var filter in metadataFilters)
                            {
                                if (!metadata.ContainsKey(filter.Key) ||
                                    !metadata[filter.Key].Equals(filter.Value, StringComparison.OrdinalIgnoreCase))
                                {
                                    matches = false;
                                    break;
                                }
                            }
                        }

                        if (matches)
                        {
                            results.Add(new S3ObjectWithMetadata
                            {
                                S3Object = s3Object,
                                Metadata = metadata,
                                PublicUrl = GetFileUrl(s3Object.Key)
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Error processing metadata for object: {Key}", s3Object.Key);
                        // Continue processing other objects
                    }
                }

                _logger.LogInformation("Found {ResultCount} objects matching metadata criteria", results.Count);
                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching objects by metadata");
                throw;
            }
        }
        private string ApplyKeyPrefix(string key)
        {
            if (string.IsNullOrEmpty(_keyPrefix) || string.IsNullOrEmpty(key))
                return key;
            
            // Ensure the prefix ends with a slash if the key doesn't already start with it
            if (!_keyPrefix.EndsWith("/") && !key.StartsWith("/"))
                return $"{_keyPrefix}/{key}";
            
            return $"{_keyPrefix}{key}";
        }

        private string RemoveKeyPrefix(string fullKey)
        {
            if (string.IsNullOrEmpty(_keyPrefix) || string.IsNullOrEmpty(fullKey))
                return fullKey;
            
            // Remove the prefix from the beginning of the key
            if (fullKey.StartsWith(_keyPrefix))
                return fullKey.Substring(_keyPrefix.Length);
            
            return fullKey;
        }

        private string GetFileUrl(string fileName)
        {
            if (_useCdn && !string.IsNullOrEmpty(_cdnDomain))
            {
                return $"https://{_cdnDomain}/{fileName}";
            }

            if (_useDirectS3Urls && !string.IsNullOrEmpty(_publicUrlTemplate))
            {
                return _publicUrlTemplate
                    .Replace("{bucket}", _bucketName)
                    .Replace("{key}", fileName);
            }

            return $"https://{_bucketName}.s3.amazonaws.com/{fileName}";
        }

        private void InvalidateFileListCache()
        {
            // Remove all cached file lists (they start with FILES_CACHE_PREFIX)
            var field = typeof(MemoryCache).GetField("_coherentState", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (field?.GetValue(_memoryCache) is object coherentState)
            {
                var entriesCollection = coherentState.GetType().GetProperty("EntriesCollection", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                if (entriesCollection?.GetValue(coherentState) is System.Collections.IDictionary entries)
                {
                    var keysToRemove = new List<object>();
                    foreach (System.Collections.DictionaryEntry entry in entries)
                    {
                        if (entry.Key.ToString().StartsWith(FILES_CACHE_PREFIX))
                        {
                            keysToRemove.Add(entry.Key);
                        }
                    }
                    foreach (var key in keysToRemove)
                    {
                        _memoryCache.Remove(key);
                    }
                }
            }
            
            _logger.LogDebug("Invalidated file list cache entries");
        }

        // Additional interface methods for ThumbnailService compatibility

        public async Task<Stream?> DownloadFileAsync(string fileName)
        {
            try
            {
                var prefixedKey = ApplyKeyPrefix(fileName);
                var request = new GetObjectRequest
                {
                    BucketName = _bucketName,
                    Key = prefixedKey
                };

                var response = await _s3Client.GetObjectAsync(request);
                return response.ResponseStream;
            }
            catch (AmazonS3Exception ex) when (ex.ErrorCode == "NoSuchKey")
            {
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error downloading file from S3: {FileName}", fileName);
                throw;
            }
        }

        public async Task<bool> FileExistsAsync(string fileName)
        {
            try
            {
                var prefixedKey = ApplyKeyPrefix(fileName);
                var request = new GetObjectMetadataRequest
                {
                    BucketName = _bucketName,
                    Key = prefixedKey
                };

                await _s3Client.GetObjectMetadataAsync(request);
                return true;
            }
            catch (AmazonS3Exception ex) when (ex.ErrorCode == "NotFound")
            {
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking file existence in S3: {FileName}", fileName);
                throw;
            }
        }

        public async Task<string> GetPublicUrlAsync(string fileName)
        {
            var prefixedKey = ApplyKeyPrefix(fileName);
            return await Task.FromResult(GetFileUrl(prefixedKey));
        }

        public async Task<DeleteResult> DeleteFileAsync(string fileName)
        {
            try
            {
                var prefixedKey = ApplyKeyPrefix(fileName);
                var request = new DeleteObjectRequest
                {
                    BucketName = _bucketName,
                    Key = prefixedKey
                };

                await _s3Client.DeleteObjectAsync(request);
                InvalidateFileListCache();

                return new DeleteResult
                {
                    Success = true,
                    DeletedAt = DateTime.UtcNow
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting file from S3: {FileName}", fileName);
                return new DeleteResult
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                // Use the key prefix in the connection test since credentials might only have access to that prefix
                var request = new ListObjectsV2Request
                {
                    BucketName = _bucketName,
                    MaxKeys = 1
                };

                // If we have a key prefix, test access to that specific path
                if (!string.IsNullOrEmpty(_keyPrefix))
                {
                    request.Prefix = _keyPrefix;
                    _logger.LogInformation("Testing S3 connection with prefix: {KeyPrefix}", _keyPrefix);
                }

                await _s3Client.ListObjectsV2Async(request);
                _logger.LogInformation("S3 connection test successful");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "S3 connection test failed");
                return false;
            }
        }

        public async Task<List<string>> ListObjectsAsync(string? prefix = null, int maxKeys = 1000)
        {
            try
            {
                var prefixedSearch = ApplyKeyPrefix(prefix ?? "");
                var request = new ListObjectsV2Request
                {
                    BucketName = _bucketName,
                    MaxKeys = maxKeys
                };

                if (!string.IsNullOrEmpty(prefixedSearch))
                    request.Prefix = prefixedSearch;

                var response = await _s3Client.ListObjectsV2Async(request);
                return response.S3Objects.Select(obj => obj.Key).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error listing objects from S3 with prefix: {Prefix}", prefix);
                throw;
            }
        }

        public async Task<bool> ObjectExistsAsync(string key)
        {
            try
            {
                var prefixedKey = ApplyKeyPrefix(key);
                var request = new GetObjectMetadataRequest
                {
                    BucketName = _bucketName,
                    Key = prefixedKey
                };

                await _s3Client.GetObjectMetadataAsync(request);
                return true;
            }
            catch (AmazonS3Exception ex) when (ex.ErrorCode == "NotFound")
            {
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking object existence in S3: {Key}", key);
                throw;
            }
        }

        private string GetContentType(string fileName)
        {
            var extension = Path.GetExtension(fileName).ToLowerInvariant();
            return extension switch
            {
                ".jpg" or ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                ".gif" => "image/gif",
                ".webp" => "image/webp",
                ".pdf" => "application/pdf",
                ".txt" => "text/plain",
                ".json" => "application/json",
                _ => "application/octet-stream"
            };
        }
    }
}
