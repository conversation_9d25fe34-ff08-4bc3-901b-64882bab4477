using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace YendorCats.API.Services.BackgroundServices
{
    /// <summary>
    /// Background service that automatically refreshes the photo index every 5 minutes
    /// </summary>
    public class PhotoIndexBackgroundService : BackgroundService
    {
        private readonly ILogger<PhotoIndexBackgroundService> _logger;
        private readonly IServiceScopeFactory _scopeFactory;
        private readonly TimeSpan _refreshInterval = TimeSpan.FromMinutes(5);

        public PhotoIndexBackgroundService(
            ILogger<PhotoIndexBackgroundService> logger,
            IServiceScopeFactory scopeFactory)
        {
            _logger = logger;
            _scopeFactory = scopeFactory;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("PhotoIndexBackgroundService started - refreshing every {Interval} minutes", _refreshInterval.TotalMinutes);

            // Wait 45 seconds after startup before first refresh to allow app to fully initialize
            await Task.Delay(TimeSpan.FromSeconds(45), stoppingToken);

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await PerformPhotoIndexRefreshAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred during automatic photo index refresh");
                }

                // Wait for the next refresh interval
                try
                {
                    await Task.Delay(_refreshInterval, stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    // Expected when cancellation is requested
                    break;
                }
            }

            _logger.LogInformation("PhotoIndexBackgroundService stopped");
        }

        private async Task PerformPhotoIndexRefreshAsync()
        {
            using var scope = _scopeFactory.CreateScope();
            var photoIndexService = scope.ServiceProvider.GetRequiredService<IPhotoIndexService>();

            try
            {
                _logger.LogInformation("Starting automatic photo index refresh at {Time}", DateTime.UtcNow);
                
                var refreshSuccess = await photoIndexService.RefreshIndexAsync();
                
                if (refreshSuccess)
                {
                    _logger.LogInformation("Automatic photo index refresh completed successfully at {Time}", DateTime.UtcNow);
                }
                else
                {
                    _logger.LogWarning("Automatic photo index refresh completed with issues or no S3 service available");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to perform automatic photo index refresh");
            }
        }

        public override async Task StopAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("PhotoIndexBackgroundService is stopping");
            await base.StopAsync(stoppingToken);
        }
    }
}