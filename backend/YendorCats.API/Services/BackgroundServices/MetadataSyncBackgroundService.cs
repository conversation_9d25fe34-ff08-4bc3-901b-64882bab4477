using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace YendorCats.API.Services.BackgroundServices
{
    /// <summary>
    /// Background service that automatically syncs S3 metadata every 3 minutes
    /// </summary>
    public class MetadataSyncBackgroundService : BackgroundService
    {
        private readonly ILogger<MetadataSyncBackgroundService> _logger;
        private readonly IServiceScopeFactory _scopeFactory;
        private readonly TimeSpan _syncInterval = TimeSpan.FromMinutes(3);

        public MetadataSyncBackgroundService(
            ILogger<MetadataSyncBackgroundService> logger,
            IServiceScopeFactory scopeFactory)
        {
            _logger = logger;
            _scopeFactory = scopeFactory;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("MetadataSyncBackgroundService started - syncing every {Interval} minutes", _syncInterval.TotalMinutes);

            // Wait 30 seconds after startup before first sync to allow app to fully initialize
            await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken);

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await PerformMetadataSyncAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred during automatic metadata sync");
                }

                // Wait for the next sync interval
                try
                {
                    await Task.Delay(_syncInterval, stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    // Expected when cancellation is requested
                    break;
                }
            }

            _logger.LogInformation("MetadataSyncBackgroundService stopped");
        }

        private async Task PerformMetadataSyncAsync()
        {
            using var scope = _scopeFactory.CreateScope();
            var metadataSyncService = scope.ServiceProvider.GetRequiredService<IMetadataSyncService>();

            try
            {
                _logger.LogInformation("Starting automatic metadata sync at {Time}", DateTime.UtcNow);
                
                var syncResult = await metadataSyncService.SyncMetadataAsync();
                
                if (syncResult.Success)
                {
                    _logger.LogInformation("Automatic metadata sync completed successfully. " +
                        "Processed: {ProcessedCount}, Added: {AddedCount}, Updated: {UpdatedCount}, Duration: {Duration}ms",
                        syncResult.ImagesProcessed, syncResult.ImagesAdded, syncResult.ImagesUpdated, syncResult.Duration.TotalMilliseconds);
                }
                else
                {
                    _logger.LogWarning("Automatic metadata sync completed with issues: {Message}", syncResult.Message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to perform automatic metadata sync");
            }
        }

        public override async Task StopAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("MetadataSyncBackgroundService is stopping");
            await base.StopAsync(stoppingToken);
        }
    }
}